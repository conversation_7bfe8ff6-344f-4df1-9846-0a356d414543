import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:flutter_hls_parser/flutter_hls_parser.dart';
import 'package:flutter_local_player/hls_utils.dart';
import 'package:flutter_local_player/custom_player_skin.dart';

class MediaKitPlayerDemo extends StatefulWidget {
  const MediaKitPlayerDemo({super.key});

  @override
  State<MediaKitPlayerDemo> createState() => _MediaKitPlayerDemoState();
}

class _MediaKitPlayerDemoState extends State<MediaKitPlayerDemo> {
  late final Player player = Player();
  late final VideoController controller = VideoController(player);
  final TextEditingController _urlController = TextEditingController();

  bool _hasSource = false;
  bool _showControls = false;
  bool _isLiveStream = false; // New state for live stream
  List<Variant> _availableQualities = [];
  int? _bufferingPercentage; // New state for buffering percentage
  String? _errorMessage; // New state for error message

  @override
  void initState() {
    super.initState();
    _urlController.text =
        'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8'; // Default URL
    player.stream.log.listen((event) {
      if (kDebugMode) {
        print('Player Log: $event');
      }
    });
    player.stream.error.listen((error) {
      if (kDebugMode) {
        print('Player Error: $error');
      }
      setState(() {
        _errorMessage = 'Error loading stream: $error';
        _bufferingPercentage = null; // Clear buffering when error occurs
      });
    });
    player.stream.buffering.listen((isBuffering) {
      if (kDebugMode) {
        print('Player Buffering: $isBuffering');
      }
      setState(() {
        if (!isBuffering) {
          _bufferingPercentage = null; // Clear percentage when not buffering
        }
        _errorMessage = null; // Clear error message when buffering starts/stops
      });
    });
    player.stream.buffer.listen((buffer) {
      if (kDebugMode) {
        print('Player Buffer: $buffer');
      }
      setState(() {
        if (player.state.duration.inMilliseconds > 0 &&
            player.state.buffering) {
          _bufferingPercentage =
              (buffer.inMilliseconds /
                      player.state.duration.inMilliseconds *
                      100)
                  .toInt();
        } else {
          _bufferingPercentage = null;
        }
      });
    });
    player.stream.playing.listen((isPlaying) {
      if (kDebugMode) {
        print('Player Playing: $isPlaying');
      }
      setState(() {
        if (isPlaying) {
          _errorMessage = null; // Clear error message when playing
        }
      });
    });
    player.stream.completed.listen((isCompleted) {
      if (kDebugMode) {
        print('Player Completed: $isCompleted');
      }
    });

    player.stream.duration.listen((duration) {
      setState(() {
        // A common heuristic for live streams is duration being zero or very large.
        // For media_kit, Duration.zero often indicates an unknown or live duration.
        _isLiveStream = duration == Duration.zero;
      });
    });
  }

  @override
  void dispose() {
    player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: TextField(
            controller: _urlController,
            decoration: const InputDecoration(
              labelText: 'Stream URL',
              border: OutlineInputBorder(),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            final url = _urlController.text;
            if (url.isNotEmpty) {
              if (kDebugMode) {
                print('Loading stream: $url');
              }
              List<Variant> variants = [];
              if (url.endsWith('.m3u8')) {
                variants = await getHLSVariants(url);
                if (kDebugMode) {
                  print('Available HLS Qualities:');
                  for (var i = 0; i < variants.length; i++) {
                    final variant = variants[i];
                    print(
                      '  Variant ${i + 1}: Resolution=${variant.format.width}x${variant.format.height}, Bandwidth=${variant.format.bitrate}',
                    );
                  }
                }
              }
              player.open(Media(url));
              player.play(); // Start playing immediately
              setState(() {
                _hasSource = true;
                _showControls = true; // Show controls immediately
                _availableQualities = variants;
              });
            }
          },
          child: const Text('Play Stream'),
        ),
        ElevatedButton(
          onPressed: () async {
            final url = _urlController.text;
            if (url.isNotEmpty) {
              if (kDebugMode) {
                print('Loading stream: $url');
              }
              List<Variant> variants = [];
              if (url.endsWith('.m3u8')) {
                variants = await getHLSVariants(url);
                if (kDebugMode) {
                  print('Available HLS Qualities:');
                  for (var i = 0; i < variants.length; i++) {
                    final variant = variants[i];
                    print(
                      '  Variant ${i + 1}: Resolution=${variant.format.width}x${variant.format.height}, Bandwidth=${variant.format.bitrate}',
                    );
                  }
                }
              }
              player.open(Media(url));
              setState(() {
                _hasSource = true;
                _showControls = false; // Do not show controls immediately
                _availableQualities = variants;
              });
            }
          },
          child: const Text('Load Stream'),
        ),
        Expanded(
          child: Center(
            child: AspectRatio(
              aspectRatio: 16 / 9,
              child: CustomPlayerSkin(
                controller: controller,
                hasSource: _hasSource,
                showControls: _showControls,
                availableQualities: _availableQualities,
                isLiveStream: _isLiveStream, // Dynamically set live indicator
                bufferingPercentage: _bufferingPercentage,
                errorMessage: _errorMessage,
                onPlayButtonPressed: () {
                  setState(() {
                    _showControls = true;
                  });
                },
              ),
            ),
          ),
        ),
      ],
    );
  }
}
